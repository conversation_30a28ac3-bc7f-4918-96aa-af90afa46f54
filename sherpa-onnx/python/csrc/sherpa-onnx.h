// sherpa-onnx/python/csrc/sherpa-onnx.h
//
// Copyright (c)  2023  Xiaomi Corporation

#ifndef SHERPA_ONNX_PYTHON_CSRC_SHERPA_ONNX_H_
#define SHERPA_ONNX_PYTHON_CSRC_SHERPA_ONNX_H_

#define NPY_NO_DEPRECATED_API NPY_1_7_API_VERSION

#include "pybind11/functional.h"
#include "pybind11/numpy.h"
#include "pybind11/pybind11.h"
#include "pybind11/stl.h"

namespace py = pybind11;

#endif  // SHERPA_ONNX_PYTHON_CSRC_SHERPA_ONNX_H_
