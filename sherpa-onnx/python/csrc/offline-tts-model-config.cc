// sherpa-onnx/python/csrc/offline-tts-model-config.cc
//
// Copyright (c)  2023  Xiaomi Corporation

#include "sherpa-onnx/python/csrc/offline-tts-model-config.h"

#include <string>

#include "sherpa-onnx/csrc/offline-tts-model-config.h"
#include "sherpa-onnx/python/csrc/offline-tts-kitten-model-config.h"
#include "sherpa-onnx/python/csrc/offline-tts-kokoro-model-config.h"
#include "sherpa-onnx/python/csrc/offline-tts-matcha-model-config.h"
#include "sherpa-onnx/python/csrc/offline-tts-vits-model-config.h"
#include "sherpa-onnx/python/csrc/offline-tts-zipvoice-model-config.h"

namespace sherpa_onnx {

void PybindOfflineTtsModelConfig(py::module *m) {
  PybindOfflineTtsVitsModelConfig(m);
  PybindOfflineTtsMatchaModelConfig(m);
  PybindOfflineTtsKokoroModelConfig(m);
  PybindOfflineTtsZipvoiceModelConfig(m);
  PybindOfflineTtsKittenModelConfig(m);

  using PyClass = OfflineTtsModelConfig;

  py::class_<PyClass>(*m, "OfflineTtsModelConfig")
      .def(py::init<>())
      .def(py::init<const OfflineTtsVitsModelConfig &,
                    const OfflineTtsMatchaModelConfig &,
                    const OfflineTtsKokoroModelConfig &,
                    const OfflineTtsZipvoiceModelConfig &,
                    const OfflineTtsKittenModelConfig &, int32_t, bool,
                    const std::string &>(),
           py::arg("vits") = OfflineTtsVitsModelConfig{},
           py::arg("matcha") = OfflineTtsMatchaModelConfig{},
           py::arg("kokoro") = OfflineTtsKokoroModelConfig{},
           py::arg("zipvoice") = OfflineTtsZipvoiceModelConfig{},
           py::arg("kitten") = OfflineTtsKittenModelConfig{},
           py::arg("num_threads") = 1, py::arg("debug") = false,
           py::arg("provider") = "cpu")
      .def_readwrite("vits", &PyClass::vits)
      .def_readwrite("matcha", &PyClass::matcha)
      .def_readwrite("kokoro", &PyClass::kokoro)
      .def_readwrite("zipvoice", &PyClass::zipvoice)
      .def_readwrite("kitten", &PyClass::kitten)
      .def_readwrite("num_threads", &PyClass::num_threads)
      .def_readwrite("debug", &PyClass::debug)
      .def_readwrite("provider", &PyClass::provider)
      .def("__str__", &PyClass::ToString);
}

}  // namespace sherpa_onnx
